import { apiSlice } from "../apiSlice";

export const tableApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getTables: builder.query({
      query: (params) => ({
        url: "/order/tables",
        method: "GET",
        params: params,
      }),
      providesTags: ["Tables"],
    }),

    retrieveTable: builder.query({
      query: (id) => ({
        url: `/order/tables/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Tables", id }],
    }),

    addTable: builder.mutation({
      query: (payload) => ({
        url: "/order/tables",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Tables"],
    }),

    patchTable: builder.mutation({
      query: (payload) => ({
        url: `/order/tables/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Tables", id },
        "Tables",
      ],
    }),

    deleteTable: builder.mutation({
      query: (id) => ({
        url: `/order/tables/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Tables"],
    }),
  }),
});

export const {
  useGetTablesQuery,
  useRetrieveTableQuery,
  useAddTableMutation,
  usePatchTableMutation,
  useDeleteTableMutation,

  useLazyGetTablesQuery,
  useLazyRetrieveTableQuery,
} = tableApiSlice;