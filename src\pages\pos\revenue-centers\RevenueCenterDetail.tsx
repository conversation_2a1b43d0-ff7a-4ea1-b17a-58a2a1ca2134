import React, { useState } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Edit, 
  Store, 
  Building2, 
  Calculator,
  Monitor,
  MoreHorizontal,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { RevenueCenter } from '@/types/pos';
import { Screen } from '@/app-components/layout/screen';
import { useRetrieveRevenueCenterQuery } from '@/redux/slices/revenueCenters';
import { useRetrieveBranchQuery } from '@/redux/slices/branches';

const RevenueCenterDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  // API hooks to fetch revenue center and branch data
  const { data: revenueCenter, isLoading, error } = useRetrieveRevenueCenterQuery(id || '');
  const { data: branchData, isLoading: loadingBranch } = useRetrieveBranchQuery(revenueCenter?.branch || '', {
    skip: !revenueCenter?.branch
  });

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading revenue center details...</p>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !revenueCenter) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">⚠️</div>
            <p className="text-muted-foreground mb-4">
              {error ? 'Error loading revenue center details' : 'Revenue center not found'}
            </p>
            <Button onClick={() => navigate('/pos/revenue-centers')}>
              Back to Revenue Centers
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/pos/revenue-centers')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <Store className="h-6 w-6" />
              <h1 className="text-3xl font-bold tracking-tight">{revenueCenter.name}</h1>
              <Badge variant={revenueCenter.is_active !== false ? 'default' : 'secondary'}>
                {revenueCenter.is_active !== false ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Revenue Center Code: {revenueCenter.revenue_center_code} • {branchData?.name || revenueCenter.branch}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link to={`/pos/revenue-centers/${revenueCenter.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Revenue Center
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Monitor className="h-4 w-4 mr-2" />
                Manage Workstations
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calculator className="h-4 w-4 mr-2" />
                Tax Configuration
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {revenueCenter.isActive ? 'Deactivate' : 'Activate'} Revenue Center
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workstations">Workstations</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Branch</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{branchData?.name || revenueCenter.branch}</div>
                    <div className="text-sm text-muted-foreground">{branchData?.branch_code || revenueCenter.branch}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Revenue Center Code</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Store className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{revenueCenter.revenue_center_code}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Status</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge variant={revenueCenter.is_active !== false ? 'default' : 'secondary'}>
                  {revenueCenter.is_active !== false ? 'Active' : 'Inactive'}
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue Center Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <p className="text-lg font-semibold">{revenueCenter.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Code</label>
                  <p className="text-lg font-semibold">{revenueCenter.revenue_center_code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Branch</label>
                  <p className="text-lg font-semibold">{branchData?.name || revenueCenter.branch}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <p className="text-lg font-semibold">
                    <Badge variant={revenueCenter.is_active !== false ? 'default' : 'secondary'}>
                      {revenueCenter.is_active !== false ? 'Active' : 'Inactive'}
                    </Badge>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>


        </TabsContent>

        <TabsContent value="workstations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Assigned Workstations</CardTitle>
              <CardDescription>
                POS devices and terminals assigned to this revenue center
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Workstations Assigned</h3>
                <p className="text-muted-foreground mb-4">
                  This revenue center doesn't have any workstations assigned yet.
                </p>
                <Link to="/pos/workstations/new">
                  <Button>
                    <Monitor className="h-4 w-4 mr-2" />
                    Add Workstation
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Center Settings</CardTitle>
              <CardDescription>
                Configuration and operational settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">General Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Code:</span>
                      <span className="font-medium">{revenueCenter.revenue_center_code}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge variant={revenueCenter.is_active !== false ? 'default' : 'secondary'}>
                        {revenueCenter.is_active !== false ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Branch:</span>
                      <span className="font-medium">{branchData?.name || revenueCenter.branch}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Configuration</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Active Status:</span>
                      <Badge variant={revenueCenter.is_active !== false ? 'default' : 'secondary'}>
                        {revenueCenter.is_active !== false ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </Screen>
  );
};

export default RevenueCenterDetail;
