import { useState } from "react";
import Logo from "@/assets/logo.png";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";

const StationCardLogin = () => {
  const navigate = useNavigate();
  const [pin, setPin] = useState("");
  const maxLength = 4;

  const handleNumberClick = (value: string) => {
    if (pin.length < maxLength) {
      setPin((prev) => prev + value);
    }
  };

  const handleClear = () => {
    setPin("");
  };

  const handleBack = () => {
    setPin((prev) => prev.slice(0, -1));
  };

  const handleEnter = () => {
    // Handle PIN submission
    if (pin.length !== maxLength) {
      toast.error("Please enter your 4 digit pin!!");
      return;
    }

    navigate("/stations/home");
    console.log("PIN submitted:", pin);
  };

  return (
    <div className="w-full min-h-screen flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-xl shadow-lg border p-6 rounded-lg flex flex-col items-center gap-2">
        {/* Logo */}
        <div className="flex justify-center mt-2 mb-2">
          <img src={Logo} alt="Logo" className="h-16 w-auto" />
        </div>

        {/* Header */}
        <div className="text-center w-full">
          <h2 className="text-2xl font-bold text-gray-900 mb-1">Enter PIN</h2>
          <p className="text-gray-500 text-sm mb-6">
            Please enter your PIN to continue
          </p>
        </div>

        {/* PIN Display */}
        <div className="flex gap-3 mb-8">
          {[...Array(maxLength)].map((_, index) => (
            <div
              key={index}
              className="w-12 h-12 border-2 rounded-lg flex items-center justify-center text-2xl"
            >
              {pin[index] ? "•" : ""}
            </div>
          ))}
        </div>

        {/* Number Pad */}
        <div className="grid grid-cols-3 gap-4 w-full">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
            <button
              key={num}
              onClick={() => handleNumberClick(num.toString())}
              className="bg-gray-100 hover:bg-gray-200 rounded-lg p-4 text-xl font-semibold"
            >
              {num}
            </button>
          ))}
          <button
            onClick={() => handleNumberClick("00")}
            className="bg-gray-100 hover:bg-gray-200 rounded-lg p-4 text-xl font-semibold"
          >
            00
          </button>
          <button
            onClick={() => handleNumberClick("0")}
            className="bg-gray-100 hover:bg-gray-200 rounded-lg p-4 text-xl font-semibold"
          >
            0
          </button>
          <button
            onClick={handleBack}
            className="bg-gray-100 hover:bg-gray-200 rounded-lg p-4 text-xl font-semibold"
          >
            ←
          </button>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-3 gap-4 w-full  mt-4">
          <button
            onClick={handleClear}
            className="bg-red-500 hover:bg-red-600 text-white rounded-lg p-4"
          >
            Clear
          </button>
          <button
            onClick={handleEnter}
            className="bg-green-500 hover:bg-green-600 text-white rounded-lg p-4"
          >
            Enter
          </button>
          <Link
            to="/stations"
            className="bg-gray-500 hover:bg-gray-600 text-white rounded-lg p-4 text-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default StationCardLogin;
