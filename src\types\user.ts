// User Management Types based on API specification

export interface User {
  id?: number;
  username?: string;
  employee_no: string;
  first_name: string;
  last_name: string;
  email: string;
  pin?: number | null;
  dob?: string | null;
  phone?: string | null;
  permit_number?: string | null;
  permit_expiry_date?: string | null;
  hire_date?: string | null;
  is_saliry_paid?: boolean;
  pays_tax?: boolean;
  reset_code?: string | null;
  reset_code_creation_time?: string | null;
  role: number;
  branch: number;
  revenue_center?: number | null;
  work_station?: number | null;
  groups?: number[];
  user_permissions?: number[];
}

export interface GETUser extends User {
  // Additional fields that might be returned in GET requests
  fullnames?: string; // Computed field combining first_name and last_name
  department?: string;
  designation?: string;
  status?: 'active' | 'inactive';
  team?: string;
  region?: string;
  manager?: string;
  phone_number?: string;
  gender?: string;
  created_date?: string;
  category?: string;
}

export interface UserRole {
  id?: number;
  name: string;
  is_active?: boolean;
}

export interface CreateUserRequest {
  user: {
    email: string;
    password: string;
  };
}

export interface UpdateUserRequest {
  username?: string;
  employee_no?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  pin?: number | null;
  dob?: string | null;
  phone?: string | null;
  permit_number?: string | null;
  permit_expiry_date?: string | null;
  hire_date?: string | null;
  is_saliry_paid?: boolean;
  pays_tax?: boolean;
  role?: number;
  branch?: number;
  revenue_center?: number | null;
  work_station?: number | null;
  groups?: number[];
  user_permissions?: number[];
}

// API Query Parameters
export interface UsersQueryParams {
  search?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

export interface UserRolesQueryParams {
  search?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

// API Response Types
export interface PaginatedUsersResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: GETUser[];
}

export interface PaginatedUserRolesResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: UserRole[];
}

// Legacy User interface for backward compatibility
export interface LegacyUser {
  id?: number;
  employee_no: string | null;
  email: string | null;
  fullnames: string | null;
  company_email?: string | null;
  gender?: string | null;
  department?: string | null;
  designation?: string | null;
  status?: 'active' | 'inactive' | null;
  team?: string | null;
  region?: string | null;
  manager?: string | null;
  phone_number?: string | null;
  created_date?: string;
  category?: string | null;
}

export type UserStatus = 'active' | 'inactive';

// Form validation schemas
export interface UserFormData {
  username: string;
  employee_no: string;
  first_name: string;
  last_name: string;
  email: string;
  pin?: number;
  dob?: string;
  phone?: string;
  permit_number?: string;
  permit_expiry_date?: string;
  hire_date?: string;
  is_saliry_paid: boolean;
  pays_tax: boolean;
  role: number;
  branch: number;
  revenue_center?: number;
  work_station?: number;
  groups: number[];
  user_permissions: number[];
}

// Permission-related types
export interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: number;
}

export interface UserPermission {
  permission_id: number;
  permission_name: string;
  comments?: string | null;
}

export interface UserGroupPermission {
  permission_id: number;
  permission_name: string;
  comments?: string | null;
}
