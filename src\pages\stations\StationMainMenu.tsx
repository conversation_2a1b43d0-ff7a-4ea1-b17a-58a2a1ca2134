import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ShoppingCartIcon,
  CoffeeIcon,
  GlassWaterIcon,
  WineIcon,
  UtensilsIcon,
  BeefIcon,
  IceCreamBowlIcon,
  SoupIcon,
  SaladIcon,
  PizzaIcon,
  CircleDotIcon,
  ArrowLeft,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";
import StationOrderItem from "@/pages/stations/components/StationOrderItem";

interface MenuItem {
  id: string;
  name: string;
  price: number;
  notes?: string;
  extras?: Array<{ label: string; price: number }>;
}

interface OrderItem extends MenuItem {
  quantity: number;
}

const StationMainMenu: React.FC = () => {
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [selectedSubgroup, setSelectedSubgroup] = useState<string | null>(null);
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [noteText, setNoteText] = useState("");
  const [isMobileOrderOpen, setIsMobileOrderOpen] = useState(false);

  // Mock data - replace with actual API calls
  const menuGroups = ["Beverages", "Main Course", "Appetizers", "Desserts"];
  const subgroups = {
    Beverages: ["Hot Drinks", "Cold Drinks", "Cocktails"],
    "Main Course": ["Pasta", "Rice", "Meat"],
    Appetizers: ["Salads", "Soups", "Starters"],
    Desserts: [],
  };
  const products = {
    "Hot Drinks": [
      { id: "1", name: "Espresso", price: 2.5 },
      { id: "2", name: "Cappuccino", price: 3.5 },
      { id: "3", name: "Latte", price: 3.75 },
    ],
    // Add more products for other subgroups
  };

  const addToOrder = (item: MenuItem) => {
    setOrderItems((prev) => {
      const existing = prev.find((i) => i.id === item.id);
      if (existing) {
        return prev.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      }
      return [...prev, { ...item, quantity: 1 }];
    });
  };

  const updateQuantity = (itemId: string, delta: number) => {
    setOrderItems(
      (prev) =>
        prev
          .map((item) => {
            if (item.id === itemId) {
              const newQuantity = Math.max(0, item.quantity + delta);
              return newQuantity === 0
                ? null
                : { ...item, quantity: newQuantity };
            }
            return item;
          })
          .filter(Boolean) as OrderItem[]
    );
  };

  const calculateItemTotal = (item: OrderItem) => {
    const basePrice = item.price * item.quantity;
    return basePrice;
  };

  const calculateOrderTotal = () => {
    return orderItems.reduce((sum, item) => sum + calculateItemTotal(item), 0);
  };

  return (
    <div className="flex flex-col lg:flex-row min-h-screen bg-background">
      <div className="w-full lg:w-1/4 p-2 lg:p-4 lg:border-r border-border/50">
        <Link to="/stations/home">
          <Button className="flex items-center justify-center gap-2 w-full mb-4 h-12 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
            <ArrowLeft className="h-4 w-4" />
            Back to Main Menu
          </Button>
        </Link>

        <Card className="shadow-lg border-border/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg lg:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Menu Groups
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-2 gap-3">
              {menuGroups.map((group) => (
                <Button
                  key={group}
                  variant={selectedGroup === group ? "default" : "outline"}
                  onClick={() => {
                    setSelectedGroup(group);
                    setSelectedSubgroup(null);
                  }}
                  className={`
                    w-full p-4 lg:p-8 text-sm lg:text-base
                    transition-all duration-300
                    hover:scale-105 hover:shadow-lg
                    flex flex-row lg:flex-col items-center justify-center gap-2
                    border-border/50
                    ${
                      selectedGroup === group
                        ? "bg-gradient-to-br from-primary to-primary/90 text-primary-foreground shadow-lg scale-105"
                        : "hover:border-primary/50 hover:bg-primary/5"
                    }
                  `}
                >
                  <span className="text-xl">
                    {group === "Beverages"
                      ? "🥤"
                      : group === "Main Course"
                      ? "🍽️"
                      : group === "Appetizers"
                      ? "🥗"
                      : "🍰"}
                  </span>
                  <span className="font-medium">{group}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {selectedGroup ? (
          <div className="space-y-4 my-4">
            <Card className="shadow-lg border-border/50">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg lg:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Subgroups
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!subgroups[selectedGroup] ||
                subgroups[selectedGroup].length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                    <div className="text-4xl lg:text-6xl mb-4 opacity-50">📋</div>
                    <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                      No Subgroups Available
                    </h3>
                    <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                      Please select a menu group or check back later for
                      available subgroups.
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-3 gap-3">
                    {subgroups[selectedGroup].map((subgroup: any) => (
                      <Button
                        key={subgroup}
                        variant={
                          selectedSubgroup === subgroup ? "default" : "outline"
                        }
                        onClick={() => setSelectedSubgroup(subgroup)}
                        className={`
                          w-full h-16 lg:h-24 flex flex-col items-center justify-center gap-1 lg:gap-2
                          transition-all duration-300 hover:scale-105 hover:shadow-lg border-border/50
                          ${selectedSubgroup === subgroup
                            ? "bg-gradient-to-br from-primary to-primary/90 text-primary-foreground shadow-lg scale-105"
                            : "hover:border-primary/50 hover:bg-primary/5"
                          }
                        `}
                      >
                        <div className="text-lg lg:text-2xl">
                          {subgroup === "Hot Drinks" ? (
                            <CoffeeIcon className="h-6 w-6" />
                          ) : subgroup === "Cold Drinks" ? (
                            <GlassWaterIcon className="h-6 w-6" />
                          ) : subgroup === "Cocktails" ? (
                            <WineIcon className="h-6 w-6" />
                          ) : subgroup === "Pasta" ? (
                            <UtensilsIcon className="h-6 w-6" />
                          ) : subgroup === "Rice" ? (
                            <IceCreamBowlIcon className="h-6 w-6" />
                          ) : subgroup === "Meat" ? (
                            <BeefIcon className="h-6 w-6" />
                          ) : subgroup === "Salads" ? (
                            <SaladIcon className="h-6 w-6" />
                          ) : subgroup === "Soups" ? (
                            <SoupIcon className="h-6 w-6" />
                          ) : subgroup === "Starters" ? (
                            <PizzaIcon className="h-6 w-6" />
                          ) : (
                            <CircleDotIcon className="h-6 w-6" />
                          )}
                        </div>
                        <span className="font-medium text-xs lg:text-sm">
                          {subgroup}
                        </span>
                      </Button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        ) : (
          <Card className="my-4 shadow-lg border-border/50">
            <CardContent className="py-8">
              <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                <div className="text-4xl lg:text-6xl mb-4 opacity-50">📋</div>
                <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                  Select a Menu Group
                </h3>
                <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                  Choose a menu group above to view available subgroups and products.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="w-full lg:w-2/4 p-2 lg:p-4">
        {selectedSubgroup ? (
          <Card className="shadow-lg border-border/50">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg lg:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Products
              </CardTitle>
            </CardHeader>
            <CardContent>
              {products[selectedSubgroup]?.length <= 0 ||
              !products[selectedSubgroup] ? (
                <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                  <div className="text-4xl lg:text-6xl mb-4 opacity-50">🍽️</div>
                  <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                    No Products Available
                  </h3>
                  <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                    Looks like this category is empty. Check back later for
                    exciting new additions to our menu!
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {products[selectedSubgroup].map((product) => (
                    <Button
                      key={product.id}
                      variant="outline"
                      onClick={() => addToOrder(product)}
                      className={`
                        w-full h-20 lg:h-24 p-3 flex flex-col justify-center items-center gap-2
                        transition-all duration-300 hover:scale-105 hover:shadow-lg border-border/50
                        ${
                          orderItems.some((item) => item.id === product.id)
                            ? "bg-gradient-to-br from-secondary/20 to-secondary/10 border-secondary text-secondary-foreground shadow-lg scale-105"
                            : "hover:border-primary/50 hover:bg-primary/5"
                        }
                      `}
                    >
                      <span className="text-xs lg:text-sm font-semibold text-center leading-tight">
                        {product.name}
                      </span>
                      <span className="text-xs lg:text-sm font-bold text-primary">
                        ${product.price.toFixed(2)}
                      </span>
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card className="shadow-lg border-border/50">
            <CardContent className="py-8">
              <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                <div className="text-4xl lg:text-6xl mb-4 opacity-50">🍽️</div>
                <h3 className="text-xl lg:text-2xl font-semibold text-foreground mb-2">
                  Select a Subgroup
                </h3>
                <p className="text-muted-foreground max-w-md text-sm lg:text-base">
                  Choose a subgroup to view available products and add them to your order.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="lg:hidden fixed bottom-4 right-6 z-50">
        <Button
          onClick={() => setIsMobileOrderOpen(!isMobileOrderOpen)}
          className="rounded-full w-16 h-16 shadow-2xl bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary hover:scale-110 transition-all duration-300"
        >
          <div className="relative">
            <ShoppingCartIcon className="h-6 w-6" />
            {orderItems.length > 0 && (
              <div className="absolute -top-3 -right-3 bg-destructive text-destructive-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold animate-pulse">
                {orderItems.length}
              </div>
            )}
          </div>
        </Button>
      </div>

      <div
        className={`
        ${isMobileOrderOpen ? "fixed inset-0 z-40 bg-background" : "hidden"}
        lg:block lg:relative lg:w-1/4 lg:p-4 lg:border-l border-border/50
        ${isMobileOrderOpen ? "p-4" : ""}
      `}
      >
        <div className="lg:hidden flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-foreground">Current Order</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsMobileOrderOpen(false)}
            className="hover:scale-105 transition-all duration-200"
          >
            ✕
          </Button>
        </div>

        <Card className="h-full shadow-lg border-border/50">
          <CardHeader className="hidden lg:block sticky top-0 bg-card z-10 border-b border-border/50">
            <CardTitle className="text-lg md:text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Current Order
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <ScrollArea className="h-[calc(100vh-400px)] lg:h-[calc(100vh-400px)] pt-2">
              {orderItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="text-4xl mb-4 opacity-50">🛒</div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Your cart is empty
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Add some delicious items to get started!
                  </p>
                </div>
              ) : (
                orderItems.map((item) => (
                  <StationOrderItem
                    key={item.id}
                    item={item}
                    calculateItemTotal={calculateItemTotal}
                    updateQuantity={updateQuantity}
                    setOrderItems={setOrderItems}
                    setNoteText={setNoteText}
                    noteText={noteText}
                  />
                ))
              )}
            </ScrollArea>

            {orderItems.length > 0 && (
              <>
                <div className="mt-4 pt-4 border-t border-border/50 sticky bottom-0 bg-card space-y-2">
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Subtotal:</span>
                    <span>${calculateOrderTotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Tax (10%):</span>
                    <span>${(calculateOrderTotal() * 0.1).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-green-600 dark:text-green-400">
                    <span>Discount (5%):</span>
                    <span>-${(calculateOrderTotal() * 0.05).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold pt-2 border-t border-border/50">
                    <span>Total:</span>
                    <span className="text-primary">
                      ${(calculateOrderTotal() * 1.05).toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="flex flex-col gap-3 mt-4 pt-4 border-t border-border/50">
                  <Button
                    className="w-full h-12 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-600 text-white font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
                    disabled={orderItems.length === 0}
                  >
                    Punch Order
                  </Button>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="w-1/2 hover:scale-105 transition-all duration-200"
                      onClick={() => setOrderItems([])}
                    >
                      Cancel Order
                    </Button>
                    <Button
                      variant="destructive"
                      className="w-1/2 hover:scale-105 transition-all duration-200"
                      asChild
                    >
                      <Link to="/stations">Log Out</Link>
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StationMainMenu;
