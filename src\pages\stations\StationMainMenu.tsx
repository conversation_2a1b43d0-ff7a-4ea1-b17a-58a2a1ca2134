import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ShoppingCartIcon,
  CoffeeIcon,
  GlassWaterIcon,
  WineIcon,
  UtensilsIcon,
  BeefIcon,
  IceCreamBowlIcon,
  SoupIcon,
  SaladIcon,
  PizzaIcon,
  CircleDotIcon,
  ArrowLeft,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";
import StationOrderItem from "@/pages/stations/components/StationOrderItem";

interface MenuItem {
  id: string;
  name: string;
  price: number;
  notes?: string;
  extras?: Array<{ label: string; price: number }>;
}

interface OrderItem extends MenuItem {
  quantity: number;
}

const StationMainMenu: React.FC = () => {
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [selectedSubgroup, setSelectedSubgroup] = useState<string | null>(null);
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [noteText, setNoteText] = useState("");
  const [isMobileOrderOpen, setIsMobileOrderOpen] = useState(false);

  // Mock data - replace with actual API calls
  const menuGroups = ["Beverages", "Main Course", "Appetizers", "Desserts"];
  const subgroups = {
    Beverages: ["Hot Drinks", "Cold Drinks", "Cocktails"],
    "Main Course": ["Pasta", "Rice", "Meat"],
    Appetizers: ["Salads", "Soups", "Starters"],
    Desserts: [],
  };
  const products = {
    "Hot Drinks": [
      { id: "1", name: "Espresso", price: 2.5 },
      { id: "2", name: "Cappuccino", price: 3.5 },
      { id: "3", name: "Latte", price: 3.75 },
    ],
    // Add more products for other subgroups
  };

  const addToOrder = (item: MenuItem) => {
    setOrderItems((prev) => {
      const existing = prev.find((i) => i.id === item.id);
      if (existing) {
        return prev.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      }
      return [...prev, { ...item, quantity: 1 }];
    });
  };

  const updateQuantity = (itemId: string, delta: number) => {
    setOrderItems(
      (prev) =>
        prev
          .map((item) => {
            if (item.id === itemId) {
              const newQuantity = Math.max(0, item.quantity + delta);
              return newQuantity === 0
                ? null
                : { ...item, quantity: newQuantity };
            }
            return item;
          })
          .filter(Boolean) as OrderItem[]
    );
  };

  const calculateItemTotal = (item: OrderItem) => {
    const basePrice = item.price * item.quantity;
    return basePrice;
  };

  const calculateOrderTotal = () => {
    return orderItems.reduce((sum, item) => sum + calculateItemTotal(item), 0);
  };

  return (
    <div className="flex flex-col lg:flex-row min-h-screen bg-gray-50">
      {/* Left Panel - Categories */}
      <div className="w-full lg:w-1/4 p-2 lg:p-4 lg:border-r">
        <Link to="/stations/home">
          <Button className="flex items-center bg-black text-white justify-center w-full mb-4">
            <ArrowLeft /> Back to Main Menu
          </Button>
        </Link>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg lg:text-xl">Menu Groups</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-2 gap-3">
              {menuGroups.map((group) => (
                <Button
                  key={group}
                  variant={selectedGroup === group ? "default" : "outline"}
                  onClick={() => {
                    setSelectedGroup(group);
                    setSelectedSubgroup(null);
                  }}
                  className={`
                    w-full p-4 lg:p-8 text-sm lg:text-base
                    transition-all duration-200 
                    hover:scale-[1.02] hover:shadow-lg
                    flex flex-row lg:flex-col items-center justify-center gap-2
                    ${
                      selectedGroup === group
                        ? "bg-primary/90 text-primary-foreground shadow-inner"
                        : "hover:bg-primary/10"
                    }
                  `}
                >
                  <span className="text-xl">
                    {group === "Beverages"
                      ? "🥤"
                      : group === "Main Course"
                      ? "🍽️"
                      : group === "Appetizers"
                      ? "🥗"
                      : "🍰"}
                  </span>
                  <span className="font-medium">{group}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {selectedGroup ? (
          <div className="space-y-4 my-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg lg:text-xl">Subgroups</CardTitle>
              </CardHeader>
              <CardContent>
                {!subgroups[selectedGroup] ||
                subgroups[selectedGroup].length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                    <div className="text-4xl lg:text-6xl mb-4">📋</div>
                    <h3 className="text-xl lg:text-2xl font-semibold text-gray-700 mb-2">
                      No Subgroups Available
                    </h3>
                    <p className="text-gray-500 max-w-md text-sm lg:text-base">
                      Please select a menu group or check back later for
                      available subgroups.
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-3 gap-2">
                    {subgroups[selectedGroup].map((subgroup: any) => (
                      <Button
                        key={subgroup}
                        variant={
                          selectedSubgroup === subgroup ? "default" : "outline"
                        }
                        onClick={() => setSelectedSubgroup(subgroup)}
                        className="w-full h-16 lg:h-24 flex flex-col items-center justify-center gap-1 lg:gap-2 transition-all hover:scale-105 hover:shadow-lg"
                      >
                        <div className="text-lg lg:text-2xl">
                          {subgroup === "Hot Drinks" ? (
                            <CoffeeIcon className="h-6 w-6" />
                          ) : subgroup === "Cold Drinks" ? (
                            <GlassWaterIcon className="h-6 w-6" />
                          ) : subgroup === "Cocktails" ? (
                            <WineIcon className="h-6 w-6" />
                          ) : subgroup === "Pasta" ? (
                            <UtensilsIcon className="h-6 w-6" />
                          ) : subgroup === "Rice" ? (
                            <IceCreamBowlIcon className="h-6 w-6" />
                          ) : subgroup === "Meat" ? (
                            <BeefIcon className="h-6 w-6" />
                          ) : subgroup === "Salads" ? (
                            <SaladIcon className="h-6 w-6" />
                          ) : subgroup === "Soups" ? (
                            <SoupIcon className="h-6 w-6" />
                          ) : subgroup === "Starters" ? (
                            <PizzaIcon className="h-6 w-6" />
                          ) : (
                            <CircleDotIcon className="h-6 w-6" />
                          )}
                        </div>
                        <span className="font-medium text-xs lg:text-sm">
                          {subgroup}
                        </span>
                      </Button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        ) : (
          <Card className="my-4">
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                <div className="text-4xl lg:text-6xl mb-4">📋</div>
                <h3 className="text-xl lg:text-2xl font-semibold text-gray-700 mb-2">
                  No Subgroups Available
                </h3>
                <p className="text-gray-500 max-w-md text-sm lg:text-base">
                  Please select a menu group or check back later for available
                  subgroups.
                </p>
              </div>
            </CardContent>{" "}
          </Card>
        )}
      </div>

      {/* Middle Panel - Products */}
      <div className="w-full lg:w-2/4 p-2 lg:p-4">
        {selectedSubgroup ? (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg lg:text-xl">Products</CardTitle>
            </CardHeader>
            <CardContent>
              {products[selectedSubgroup]?.length <= 0 ||
              !products[selectedSubgroup] ? (
                <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                  <div className="text-4xl lg:text-6xl mb-4">🍽️</div>
                  <h3 className="text-xl lg:text-2xl font-semibold text-gray-700 mb-2">
                    No Products Available
                  </h3>
                  <p className="text-gray-500 max-w-md text-sm lg:text-base">
                    Looks like this category is empty. Check back later for
                    exciting new additions to our menu!
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-2">
                  {products[selectedSubgroup].map((product) => (
                    <Button
                      key={product.id}
                      variant="outline"
                      onClick={() => addToOrder(product)}
                      className={`w-full h-16 lg:h-auto p-2 lg:p-4 text-left flex flex-col lg:flex-row justify-center items-center gap-1 lg:gap-2 ${
                        orderItems.some((item) => item.id === product.id)
                          ? "bg-orange-100 border-orange-200 hover:bg-orange-200"
                          : ""
                      }`}
                    >
                      <span className="text-xs lg:text-sm font-medium">
                        {product.name}
                      </span>
                      <span className="text-xs lg:text-sm">
                        ${product.price.toFixed(2)}
                      </span>
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 lg:py-12 text-center">
                <div className="text-4xl lg:text-6xl mb-4">🍽️</div>
                <h3 className="text-xl lg:text-2xl font-semibold text-gray-700 mb-2">
                  No Products Available
                </h3>
                <p className="text-gray-500 max-w-md text-sm lg:text-base">
                  Looks like this category is empty. Check back later for
                  exciting new additions to our menu!
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Mobile Order Toggle Button */}
      <div className="lg:hidden fixed bottom-4 right-6 z-50">
        <Button
          onClick={() => setIsMobileOrderOpen(!isMobileOrderOpen)}
          className="rounded-full w-14 h-14 shadow-lg"
        >
          <div className="text-sm font-bold relative gap-1">
            <ShoppingCartIcon />{" "}
            <div className=" absolute -top-8 -right-7 bg-white border border-destructive rounded-full text-destructive w-8 h-8 flex items-center justify-center ">
              {orderItems.length}
            </div>
          </div>
        </Button>
      </div>

      {/* Right Panel - Order Summary */}
      <div
        className={`
        ${isMobileOrderOpen ? "fixed inset-0 z-40 bg-white" : "hidden"} 
        lg:block lg:relative lg:w-1/4 lg:p-4 lg:border-l
        ${isMobileOrderOpen ? "p-4" : ""}
      `}
      >
        {/* Mobile close button */}
        <div className="lg:hidden flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Current Order</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsMobileOrderOpen(false)}
          >
            ✕
          </Button>
        </div>

        <Card className="h-full">
          <CardHeader className="hidden lg:block md:sticky md:top-0 bg-white z-10">
            <CardTitle className="text-lg md:text-xl">Current Order</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[calc(100vh-400px)] lg:h-[calc(100vh-400px)] pt-2">
              {orderItems.map((item) => (
                <StationOrderItem
                  key={item.id}
                  item={item}
                  calculateItemTotal={calculateItemTotal}
                  updateQuantity={updateQuantity}
                  setOrderItems={setOrderItems}
                  setNoteText={setNoteText}
                  noteText={noteText}
                />
              ))}
            </ScrollArea>
            <div className="mt-4 pt-4 border-t sticky bottom-0 bg-white space-y-2">
              <div className="flex justify-between text-xs">
                <span>Subtotal:</span>
                <span>${calculateOrderTotal().toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Tax (10%):</span>
                <span>${(calculateOrderTotal() * 0.1).toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-xs text-green-600">
                <span>Discount (5%):</span>
                <span>-${(calculateOrderTotal() * 0.05).toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-lg md:text-lg font-bold pt-2 border-t">
                <span>Total:</span>
                <span className="text-primary">
                  ${(calculateOrderTotal() * 1.05).toFixed(2)}
                </span>
              </div>
            </div>
            <div className="flex flex-col gap-2 mt-4 pt-4 border-t">
              <Button
                className="w-full bg-success text-white"
                disabled={orderItems.length === 0}
              >
                Punch Order
              </Button>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="w-1/2"
                  onClick={() => setOrderItems([])}
                >
                  Cancel Order
                </Button>
                <Button variant="destructive" className="w-1/2" asChild>
                  <Link to="/stations">Log Out</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StationMainMenu;
