import React from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Menu,
  Search,
  CalendarRange,
  CreditCard,
  FileText,
  LogOut,
  BarChart3,
} from "lucide-react";
import Logo from "@/assets/logo.png";

const StationLandingPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50">
      <Card className="w-full max-w-4xl py-8">
        <div className="flex justify-center mt-2 mb-6">
          <img src={Logo} alt="Logo" className="h-16 w-auto" />
        </div>
        <CardHeader className="text-center mb-6">
          <CardTitle className="text-2xl">POS Station Menu</CardTitle>
          <CardDescription>Select an option to proceed</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4 px-8">
          <Button
            variant="outline"
            size="lg"
            className="h-24 flex flex-col items-center justify-center gap-2"
            asChild
          >
            <Link to="/stations/main-menu">
              <Menu className="h-6 w-6" />
              <span>Main Menu</span>
            </Link>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-24 flex flex-col items-center justify-center gap-2"
            asChild
          >
            <Link to="/stations/room-enquiry">
              <Search className="h-6 w-6" />
              <span>Room Enquiry</span>
            </Link>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-24 flex flex-col items-center justify-center gap-2"
            asChild
          >
            <Link to="/stations/functions">
              <CalendarRange className="h-6 w-6" />
              <span>Functions</span>
            </Link>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-24 flex flex-col items-center justify-center gap-2"
            asChild
          >
            <Link to="/stations/card-creation">
              <CreditCard className="h-6 w-6" />
              <span>Card Creation</span>
            </Link>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-24 flex flex-col items-center justify-center gap-2"
            asChild
          >
            <Link to="/stations/view-checks">
              <FileText className="h-6 w-6" />
              <span>View Checks</span>
            </Link>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-24 flex flex-col items-center justify-center gap-2"
            asChild
          >
            <Link to="/stations/reports">
              <BarChart3 className="h-6 w-6" />
              <span>Reports</span>
            </Link>
          </Button>
        </CardContent>

        <div className="flex justify-center mt-8 w-full px-8 ">
          <Button
            variant="destructive"
            size="lg"
            className="flex items-center gap-2 w-full py-6"
            asChild
          >
            <Link to="/stations">
              <LogOut className="h-5 w-5" />
              <span>Log Out</span>
            </Link>
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default StationLandingPage;
