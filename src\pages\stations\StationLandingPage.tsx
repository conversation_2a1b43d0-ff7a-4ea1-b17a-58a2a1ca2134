import React from "react";
import { <PERSON> } from "react-router-dom";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Menu,
  Search,
  CalendarRange,
  CreditCard,
  FileText,
  LogOut,
  BarChart3,
} from "lucide-react";
import Logo from "@/assets/logo.png";

const StationLandingPage: React.FC = () => {
  const menuItems = [
    { icon: Menu, label: "Main Menu", path: "/stations/main-menu", color: "from-blue-500 to-blue-600" },
    { icon: Search, label: "Room Enquiry", path: "/stations/room-enquiry", color: "from-green-500 to-green-600" },
    { icon: CalendarRange, label: "Functions", path: "/stations/functions", color: "from-purple-500 to-purple-600" },
    { icon: CreditCard, label: "Card Creation", path: "/stations/card-creation", color: "from-orange-500 to-orange-600" },
    { icon: FileText, label: "View Checks", path: "/stations/view-checks", color: "from-teal-500 to-teal-600" },
    { icon: BarChart3, label: "Reports", path: "/stations/reports", color: "from-indigo-500 to-indigo-600" },
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-5xl shadow-2xl border-border/50 backdrop-blur-sm">
        <CardHeader className="text-center space-y-6 pb-8">
          <div className="flex justify-center">
            <div className="relative">
              <img
                src={Logo}
                alt="Logo"
                className="h-20 w-auto drop-shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent rounded-full blur-xl" />
            </div>
          </div>
          <div className="space-y-3">
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              POS Station Menu
            </CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Select an option to proceed with your tasks
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="px-8 pb-8 space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {menuItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <Button
                  key={index}
                  variant="outline"
                  size="lg"
                  className="h-32 flex flex-col items-center justify-center gap-4 hover:scale-105 transition-all duration-300 hover:shadow-xl border-border/50 hover:border-primary/50 group relative overflow-hidden"
                  asChild
                >
                  <Link to={item.path}>
                    <div className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`} />
                    <IconComponent className="h-8 w-8 group-hover:text-primary transition-colors duration-300" />
                    <span className="font-semibold text-base group-hover:text-primary transition-colors duration-300">
                      {item.label}
                    </span>
                  </Link>
                </Button>
              );
            })}
          </div>

          <div className="flex justify-center pt-4">
            <Button
              variant="destructive"
              size="lg"
              className="h-16 px-12 flex items-center gap-3 text-lg font-semibold hover:scale-105 transition-all duration-200 shadow-lg"
              asChild
            >
              <Link to="/stations">
                <LogOut className="h-6 w-6" />
                Log Out
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StationLandingPage;
