# Toast Notifications Implementation Guide

This guide explains how toast notifications have been implemented throughout the application for better user feedback and error handling.

## Overview

Toast notifications have been added to provide immediate feedback to users for:
- ✅ **Success operations** (create, update, delete)
- ❌ **Error handling** (API errors, validation errors, network issues)
- ℹ️ **Information messages** (loading states, warnings)

## Implementation Details

### 1. Error Handling Utility (`src/utils/errorHandling.ts`) 

A comprehensive utility has been created to handle different types of errors consistently:

```typescript
import { handleApiError, handleApiSuccess, showErrorToast, showSuccessToast } from '@/utils/errorHandling';

// For API operations
try {
  const result = await createBranch(data).unwrap();
  handleApiSuccess('Branch created successfully!', result);
} catch (error) {
  handleApiError(error, 'create branch');
}

// For custom messages
showSuccessToast('Custom success message');
showErrorToast(error, 'Custom error message');
```

### 2. Features of Error Handling Utility

- **Smart Error Extraction**: Automatically extracts meaningful error messages from various API response formats
- **RTK Query Support**: Handles RTK Query error structures
- **Validation Errors**: Properly displays field-specific validation errors
- **Network Errors**: Handles network connectivity issues
- **Fallback Messages**: Provides sensible defaults when specific errors aren't available

### 3. Updated Components

#### Branch Management
- **BranchForm.tsx**: Success/error notifications for create and update operations
- **branches/index.tsx**: Success/error notifications for delete operations and loading errors

#### Supplier Management  
- **AddSupplier.tsx**: Success/error notifications for supplier creation
- **Supplier/index.tsx**: Error notifications for loading failures

### 4. API Response Handling

Added `transformResponse` functions to handle different API response structures:

```typescript
transformResponse: (response: any) => {
  console.log('API response:', response);
  
  // Handle different response structures
  if (Array.isArray(response)) {
    return response;
  } else if (response && response.data) {
    return response.data;
  } else if (response && response.results) {
    return response.results;
  }
  
  return response;
}
```

## Usage Examples

### 1. Basic Success/Error Handling

```typescript
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';

const handleSubmit = async (data) => {
  try {
    const result = await apiCall(data).unwrap();
    handleApiSuccess('Operation completed successfully!', result);
    // Navigate or update UI
  } catch (error) {
    handleApiError(error, 'perform operation');
  }
};
```

### 2. Custom Toast Messages

```typescript
import { showSuccessToast, showErrorToast, showInfoToast, showWarningToast } from '@/utils/errorHandling';

// Different types of notifications
showSuccessToast('Data saved successfully!');
showErrorToast(error, 'Failed to save data');
showInfoToast('Processing your request...');
showWarningToast('Please check your input');
```

### 3. Loading State with Error Handling

```typescript
const { data, isLoading, error } = useGetDataQuery();

useEffect(() => {
  if (error) {
    handleApiError(error, 'load data');
  }
}, [error]);
```

## Error Message Extraction

The utility handles various error formats:

1. **RTK Query Errors**: `error.data.message`, `error.data.detail`
2. **Validation Errors**: Field-specific errors from API
3. **Network Errors**: Connection and timeout issues
4. **Standard Errors**: `error.message`
5. **String Errors**: Direct error strings

## Best Practices

### 1. Consistent Error Handling
Always use the utility functions for consistent error handling across the app:

```typescript
// ✅ Good
handleApiError(error, 'create user');

// ❌ Avoid
console.error(error);
toast.error('Something went wrong');
```

### 2. Meaningful Operation Descriptions
Use descriptive operation names for better error messages:

```typescript
// ✅ Good
handleApiError(error, 'create new branch');
handleApiError(error, 'update user profile');

// ❌ Less helpful
handleApiError(error, 'operation');
```

### 3. Success Messages with Context
Provide specific success messages:

```typescript
// ✅ Good
handleApiSuccess('Branch created successfully!');
handleApiSuccess('User profile updated!');

// ❌ Generic
handleApiSuccess('Success!');
```

## Debugging Features

### 1. Console Logging
All API responses are logged to the console for debugging:

```typescript
transformResponse: (response: any) => {
  console.log('API response:', response); // Debug logging
  return processResponse(response);
}
```

### 2. Error Details
Detailed error information is logged:

```typescript
export const handleApiError = (error: any, operation: string) => {
  console.error(`Error during ${operation}:`, error); // Debug logging
  showErrorToast(error, `Failed to ${operation}. Please try again.`);
};
```

## Integration with Existing Components

The toast notifications are integrated with:
- ✅ **Sonner** toast library (already in use)
- ✅ **RTK Query** for API state management
- ✅ **React Hook Form** for form validation
- ✅ **React Router** for navigation after operations

## Future Enhancements

1. **Loading Toasts**: Add loading indicators for long operations
2. **Retry Functionality**: Add retry buttons to error toasts
3. **Offline Support**: Handle offline/online state changes
4. **Toast Persistence**: Option to persist important messages
5. **Custom Toast Components**: Branded toast designs

## Testing

To test the toast notifications:

1. **Success Cases**: Create/update/delete operations
2. **Error Cases**: Invalid data, network issues, server errors
3. **Edge Cases**: Empty responses, malformed data
4. **Loading States**: Slow network conditions

The implementation provides a robust foundation for user feedback throughout the application while maintaining consistency and good user experience.
