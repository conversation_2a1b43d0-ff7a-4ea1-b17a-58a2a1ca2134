import { apiSlice } from "../apiSlice";

// Types for Revenue Center API based on API specification
export interface RevenueCenter {
  id?: number;
  revenue_center_code: string;
  name: string;
  is_active?: boolean;
  branch: string;
}

export const revenueCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all revenue centers
    getRevenueCenters: builder.query<RevenueCenter[], any>({
      query: (params) => ({
        url: "/setup/revenue_centre",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        // Handle different response structures
        if (Array.isArray(response)) {
          return response;
        } else if (response && response.data) {
          if (Array.isArray(response.data)) {
            return response.data;
          } else if (response.data.revenue_centers && Array.isArray(response.data.revenue_centers)) {
            return response.data.revenue_centers;
          } else if (response.data.results && Array.isArray(response.data.results)) {
            return response.data.results;
          } else {
            return [response.data];
          }
        } else if (response && response.results && Array.isArray(response.results)) {
          return response.results;
        } else {
          return [];
        }
      },
      providesTags: ["RevenueCenters"],
    }),

    retrieveRevenueCenter: builder.query<RevenueCenter, string>({
      query: (id) => ({
        url: `/setup/revenue_centre/${id}`,
        method: "GET",
      }),
      transformResponse: (response: any) => {
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      providesTags: (result, error, id) => [{ type: "RevenueCenters", id }],
    }),

    addRevenueCenter: builder.mutation<RevenueCenter, Partial<RevenueCenter>>({
      query: (payload) => ({
        url: "/setup/revenue_centre",
        method: "POST",
        body: payload,
      }),
      transformResponse: (response: any) => {
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      transformErrorResponse: (response: any) => {
        if (typeof response.data === 'string' && response.data.includes('<!DOCTYPE')) {
          return {
            status: response.status,
            data: {
              message: 'Server error occurred. Please check the server logs.',
              details: 'The server returned an HTML error page instead of JSON.'
            }
          };
        }
        return response;
      },
      invalidatesTags: ["RevenueCenters"],
    }),

    patchRevenueCenter: builder.mutation<RevenueCenter, { id: string; data: Partial<RevenueCenter> }>({
      query: ({ id, data }) => ({
        url: `/setup/revenue_centre/${id}`,
        method: "PATCH",
        body: data,
      }),
      transformResponse: (response: any) => {
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      invalidatesTags: (result, error, { id }) => [{ type: "RevenueCenters", id }, "RevenueCenters"],
    }),

    deleteRevenueCenter: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/revenue_centre/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RevenueCenters"],
    }),
  }),
});

export const {
  useGetRevenueCentersQuery,
  useRetrieveRevenueCenterQuery,
  useAddRevenueCenterMutation,
  usePatchRevenueCenterMutation,
  useDeleteRevenueCenterMutation,

  useLazyGetRevenueCentersQuery,
  useLazyRetrieveRevenueCenterQuery,
} = revenueCenterApiSlice;
