import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';

import { Checkbox } from '@/components/ui/checkbox';
import { Screen } from '@/app-components/layout/screen';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X, Monitor, Printer } from 'lucide-react';
import { WorkstationFormData, WorkstationRole } from '@/types/pos';

interface WorkstationFormProps {
  mode: 'create' | 'edit';
}

// TODO: Replace with actual API calls
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetRevenueCentersQuery } from '@/redux/slices/revenueCenters';
import { useGetPrintersQuery } from '@/redux/slices/printers';

const WorkstationForm: React.FC<WorkstationFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isLoading, setIsLoading] = useState(false);

  // API hooks
  const { data: branches = [], isLoading: loadingBranches } = useGetBranchesQuery({});
  const { data: revenueCenters = [], isLoading: loadingRevenueCenters } = useGetRevenueCentersQuery({});
  const { data: printers = [], isLoading: loadingPrinters } = useGetPrintersQuery({});

  const form = useForm<WorkstationFormData>({
    defaultValues: {
      name: '',
      branch: '',
      revenue_center: '',
      role: WorkstationRole.FULL_POS,
      ip_address: '',
      hostname: '',
      linked_printer: '',
      supports_magnetic_card: true,
      supports_employee_id_login: true,
      is_online: true,
      language: 'en'
    },
  });

  const watchBranch = form.watch('branch');

  const onSubmit = async (data: WorkstationFormData) => {
    setIsLoading(true);
    try {
      console.log('Submitting workstation data:', data);
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate('/pos/workstations');
    } catch (error) {
      console.error('Error saving workstation:', error);
    } finally {
      setIsLoading(false);
    }
  };



  // Filter revenue centers by selected branch
  const filteredRevenueCenters = Array.isArray(revenueCenters) && watchBranch
    ? revenueCenters.filter(rc => rc.branch?.toString() === watchBranch?.toString())
    : [];

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/workstations')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add Workstation' : 'Edit Workstation'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Set up a new POS device or terminal'
              : 'Update workstation configuration and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the basic details for the workstation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                rules={{ required: 'Workstation name is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Workstation Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Main Counter POS, Waiter Tablet 1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="branch"
                rules={{ required: 'Branch assignment is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assign to Branch *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingBranches ? (
                          <SelectItem value="loading" disabled>Loading branches...</SelectItem>
                        ) : Array.isArray(branches) && branches.length === 0 ? (
                          <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                        ) : Array.isArray(branches) ? (
                          branches.map((branch) => (
                            <SelectItem
                              key={branch.id}
                              value={branch.id?.toString() || `branch-${branch.id}`}
                            >
                              {branch.name} ({branch.branch_code})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="revenue_center"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assign to Revenue Center</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={!watchBranch}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select revenue center (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingRevenueCenters ? (
                          <SelectItem value="loading" disabled>Loading revenue centers...</SelectItem>
                        ) : !watchBranch ? (
                          <SelectItem value="no-branch" disabled>Select a branch first</SelectItem>
                        ) : filteredRevenueCenters.length === 0 ? (
                          <SelectItem value="no-revenue-centers" disabled>
                            No revenue centers for selected branch
                          </SelectItem>
                        ) : (
                          filteredRevenueCenters.map((rc) => (
                            <SelectItem key={rc.id} value={rc.id?.toString() || `rc-${rc.id}`}>
                              {rc.name} ({rc.revenue_center_code})
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Optional: Assign to a specific revenue center within the branch
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                rules={{ required: 'Workstation role is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Workstation Role *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select workstation role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(WorkstationRole).map((role) => (
                          <SelectItem key={role} value={role}>
                            {role}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the role and capabilities of this workstation
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Network Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Network Configuration</CardTitle>
              <CardDescription>
                Configure network settings for the workstation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="ip_address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>IP Address</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g., *************" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Static IP address for the workstation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hostname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hostname</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g., pos-main-01" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Network hostname for the device
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="supports_magnetic_card"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Touchscreen Compatible
                      </FormLabel>
                      <FormDescription>
                        Enable if this workstation supports touch input
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Printer Assignment */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Printer className="h-5 w-5" />
                <span>Printer Assignment</span>
              </CardTitle>
              <CardDescription>
                Assign printers to this workstation for receipts and kitchen orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="linked_printer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Printers</FormLabel>
                    <FormDescription>
                      Select printers that this workstation can use
                    </FormDescription>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      {loadingPrinters ? (
                        <div className="col-span-2 text-center text-muted-foreground">
                          Loading printers...
                        </div>
                      ) : Array.isArray(printers) && printers.length === 0 ? (
                        <div className="col-span-2 text-center text-muted-foreground">
                          No printers available
                        </div>
                      ) : Array.isArray(printers) ? (
                        printers.map((printer) => (
                          <div key={printer.id} className="flex items-center space-x-2 p-3 border rounded-lg">
                            <Checkbox
                              id={printer.id?.toString()}
                              checked={field.value === printer.id?.toString()}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  field.onChange(printer.id?.toString());
                                } else {
                                  field.onChange('');
                                }
                              }}
                            />
                            <div className="flex-1">
                              <Label htmlFor={printer.id?.toString()} className="font-medium">
                                {printer.name}
                              </Label>
                              <div className="text-sm text-muted-foreground">
                                {printer.printer_purpose || 'General'} Printer
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="col-span-2 text-center text-muted-foreground">
                          No printers available
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate('/pos/workstations')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create Workstation' : 'Update Workstation'}
            </Button>
          </div>
        </form>
      </Form>
      </div>
    </Screen>
  );
};

export default WorkstationForm;
