import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Minus, Trash2, Edit3, PlusCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";

type Props = {
  item: any;
  noteText: string;
  calculateItemTotal: (item: any) => number;
  updateQuantity: (itemId: string, delta: number) => void;
  setOrderItems: React.Dispatch<React.SetStateAction<any[]>>;
  setNoteText: React.Dispatch<React.SetStateAction<string>>;
};

// Mock extras data
const availableExtras = [
  { id: "extra1", label: "Extra Sugar", price: 0.5 },
  { id: "extra2", label: "Extra Cream", price: 0.75 },
  { id: "extra3", label: "Extra Shot", price: 1.0 },
  { id: "extra4", label: "Caramel Syrup", price: 0.8 },
  { id: "extra5", label: "Vanilla Syrup", price: 0.8 },
];

const StationOrderItem = ({
  item,
  noteText,
  calculateItemTotal,
  updateQuantity,
  setOrderItems,
  setNoteText,
}: Props) => {
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  const handleAddNote = (itemId: string) => {
    setOrderItems((prev) =>
      prev.map((item) =>
        item.id === itemId ? { ...item, notes: noteText } : item
      )
    );
    setNoteText("");
  };

  const handleAddExtras = (
    itemId: string,
    extra: { label: string; price: number },
    checked: boolean
  ) => {
    setOrderItems((prev) =>
      prev.map((item) => {
        if (item.id === itemId) {
          const currentExtras = item.extras || [];
          if (checked) {
            return {
              ...item,
              extras: [...currentExtras, extra],
              price: item.price + extra.price,
            };
          } else {
            return {
              ...item,
              extras: currentExtras.filter((e: any) => e.label !== extra.label),
              price: item.price - extra.price,
            };
          }
        }
        return item;
      })
    );
  };

  return (
    <div className="flex flex-col gap-2 mb-4 p-2 border rounded hover:shadow-sm transition-shadow">
      <div className="flex justify-between items-center flex-wrap">
        <span className="font-bold text-sm">{item.name}</span>
        <span className="text-primary text-sm font-bold">
          ${calculateItemTotal(item).toFixed(2)}
        </span>
      </div>

      <div className="flex items-center gap-2 flex-wrap">
        <div className="flex items-center gap-1 text-xs">
          <Button
            size="sm"
            variant="outline"
            onClick={() => updateQuantity(item.id, -1)}
            className="h-8 w-8 p-0"
          >
            <Minus className="h-3 w-3" />
          </Button>
          <Input
            type="number"
            value={item.quantity}
            onChange={(e) => {
              const newQuantity = parseInt(e.target.value) || 0;
              updateQuantity(item.id, newQuantity - item.quantity);
            }}
            className="w-16 text-center h-8"
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => updateQuantity(item.id, 1)}
            className="h-8 w-8 p-0"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
        {item.notes && (
          <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
            Note: {item.notes}
          </div>
        )}
        {item.extras && item.extras.length > 0 && (
          <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
            Extras:{" "}
            {item.extras
              .map(
                (extra: any) => `${extra.label} (+$${extra.price.toFixed(2)})`
              )
              .join(", ")}
          </div>
        )}

        <div className="flex gap-1 flex-wrap">
          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedItemId(item.id)}
                className="h-8"
              >
                <PlusCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">Extras</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add Extras</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                {availableExtras.map((extra) => (
                  <div key={extra.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={extra.id}
                      checked={item.extras?.some(
                        (e: any) => e.label === extra.label
                      )}
                      onCheckedChange={(checked) => {
                        handleAddExtras(
                          item.id,
                          {
                            label: extra.label,
                            price: extra.price,
                          },
                          checked as boolean
                        );
                      }}
                    />
                    <Label htmlFor={extra.id}>
                      {extra.label} (+${extra.price.toFixed(2)})
                    </Label>
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedItemId(item.id);
                  setNoteText(item.notes || "");
                }}
                className="h-8"
              >
                <Edit3 className="h-3 w-3 mr-1" />
                <span className="text-xs">Note</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add Note</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <Textarea
                  placeholder="Add special instructions..."
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                  className="min-h-[100px]"
                />
                <Button onClick={() => handleAddNote(item.id)}>
                  Save Note
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            size="sm"
            variant="destructive"
            onClick={() => updateQuantity(item.id, -item.quantity)}
            className="h-8 w-8 p-0"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StationOrderItem;
