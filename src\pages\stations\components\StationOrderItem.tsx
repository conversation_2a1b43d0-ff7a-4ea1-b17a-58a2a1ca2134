import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Minus, Trash2, Edit3, PlusCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";

type Props = {
  item: any;
  noteText: string;
  calculateItemTotal: (item: any) => number;
  updateQuantity: (itemId: string, delta: number) => void;
  setOrderItems: React.Dispatch<React.SetStateAction<any[]>>;
  setNoteText: React.Dispatch<React.SetStateAction<string>>;
};

// Mock extras data
const availableExtras = [
  { id: "extra1", label: "Extra Sugar", price: 0.5 },
  { id: "extra2", label: "Extra Cream", price: 0.75 },
  { id: "extra3", label: "Extra Shot", price: 1.0 },
  { id: "extra4", label: "Caramel Syrup", price: 0.8 },
  { id: "extra5", label: "Vanilla Syrup", price: 0.8 },
];

const StationOrderItem = ({
  item,
  noteText,
  calculateItemTotal,
  updateQuantity,
  setOrderItems,
  setNoteText,
}: Props) => {
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  const handleAddNote = (itemId: string) => {
    setOrderItems((prev) =>
      prev.map((item) =>
        item.id === itemId ? { ...item, notes: noteText } : item
      )
    );
    setNoteText("");
  };

  const handleAddExtras = (
    itemId: string,
    extra: { label: string; price: number },
    checked: boolean
  ) => {
    setOrderItems((prev) =>
      prev.map((item) => {
        if (item.id === itemId) {
          const currentExtras = item.extras || [];
          if (checked) {
            return {
              ...item,
              extras: [...currentExtras, extra],
              price: item.price + extra.price,
            };
          } else {
            return {
              ...item,
              extras: currentExtras.filter((e: any) => e.label !== extra.label),
              price: item.price - extra.price,
            };
          }
        }
        return item;
      })
    );
  };

  return (
    <div className="flex flex-col gap-3 mb-4 p-4 border border-border/50 rounded-lg hover:shadow-lg transition-all duration-300 bg-card/50 backdrop-blur-sm">
      <div className="flex justify-between items-center">
        <span className="font-bold text-sm text-foreground">{item.name}</span>
        <span className="text-primary text-sm font-bold bg-primary/10 px-2 py-1 rounded-md">
          ${calculateItemTotal(item).toFixed(2)}
        </span>
      </div>

      <div className="flex items-center gap-3 flex-wrap">
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => updateQuantity(item.id, -1)}
            className="h-8 w-8 p-0 hover:scale-110 transition-all duration-200 border-border/50 hover:border-destructive/50 hover:text-destructive"
          >
            <Minus className="h-3 w-3" />
          </Button>
          <Input
            type="number"
            value={item.quantity}
            onChange={(e) => {
              const newQuantity = parseInt(e.target.value) || 0;
              updateQuantity(item.id, newQuantity - item.quantity);
            }}
            className="w-16 text-center h-8 border-border/50 bg-background/50"
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => updateQuantity(item.id, 1)}
            className="h-8 w-8 p-0 hover:scale-110 transition-all duration-200 border-border/50 hover:border-primary/50 hover:text-primary"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {item.notes && (
          <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded-md border border-border/30">
            <span className="font-medium">Note:</span> {item.notes}
          </div>
        )}

        {item.extras && item.extras.length > 0 && (
          <div className="text-xs text-muted-foreground bg-secondary/10 p-2 rounded-md border border-secondary/20">
            <span className="font-medium">Extras:</span>{" "}
            {item.extras
              .map(
                (extra: any) => `${extra.label} (+$${extra.price.toFixed(2)})`
              )
              .join(", ")}
          </div>
        )}

        <div className="flex gap-2 flex-wrap">
          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedItemId(item.id)}
                className="h-8 hover:scale-105 transition-all duration-200 border-border/50 hover:border-secondary/50 hover:text-secondary"
              >
                <PlusCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">Extras</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] border-border/50">
              <DialogHeader>
                <DialogTitle className="text-foreground">Add Extras</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                {availableExtras.map((extra) => (
                  <div key={extra.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <Checkbox
                      id={extra.id}
                      checked={item.extras?.some(
                        (e: any) => e.label === extra.label
                      )}
                      onCheckedChange={(checked) => {
                        handleAddExtras(
                          item.id,
                          {
                            label: extra.label,
                            price: extra.price,
                          },
                          checked as boolean
                        );
                      }}
                      className="border-border/50"
                    />
                    <Label htmlFor={extra.id} className="text-foreground cursor-pointer flex-1">
                      {extra.label}
                      <span className="text-primary font-semibold ml-2">
                        (+${extra.price.toFixed(2)})
                      </span>
                    </Label>
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedItemId(item.id);
                  setNoteText(item.notes || "");
                }}
                className="h-8 hover:scale-105 transition-all duration-200 border-border/50 hover:border-blue-500/50 hover:text-blue-600"
              >
                <Edit3 className="h-3 w-3 mr-1" />
                <span className="text-xs">Note</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] border-border/50">
              <DialogHeader>
                <DialogTitle className="text-foreground">Add Note</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <Textarea
                  placeholder="Add special instructions..."
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                  className="min-h-[100px] border-border/50 bg-background/50"
                />
                <Button
                  onClick={() => handleAddNote(item.id)}
                  className="hover:scale-105 transition-all duration-200"
                >
                  Save Note
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            size="sm"
            variant="destructive"
            onClick={() => updateQuantity(item.id, -item.quantity)}
            className="h-8 w-8 p-0 hover:scale-110 transition-all duration-200 shadow-md"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StationOrderItem;
