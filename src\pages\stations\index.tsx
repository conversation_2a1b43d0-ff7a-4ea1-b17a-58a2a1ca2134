import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CreditCard, KeyRound } from "lucide-react";
import Logo from "@/assets/logo.png";

const StationWelcome: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-xl py-8">
        {/* Logo */}
        <div className="flex justify-center mt-2 mb-2">
          <img src={Logo} alt="Logo" className="h-16 w-auto" />
        </div>
        <CardHeader className="text-center my-3">
          <CardTitle className="text-2xl">Welcome to GMC POS</CardTitle>
          <CardDescription>Please select your login method</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            variant="outline"
            size="lg"
            className="w-full flex items-center justify-center gap-2 py-8"
            asChild
          >
            <Link to="/stations/pin-login">
              <KeyRound className="mr-2" />
              Login with PIN
            </Link>
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="w-full flex items-center justify-center gap-2 py-8"
            asChild
          >
            <Link to="/stations/card-login">
              <CreditCard className="mr-2" />
              Login with Card
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default StationWelcome;
